#!/usr/bin/env python3
"""
Generate an animated GIF of puzzle pieces joining and filling with red color.
"""

from PIL import Image, ImageDraw
import math

def create_puzzle_base_path(draw, x, y, width, height, fill_height=0):
    """Draw the main puzzle base with slots for the moving piece."""
    # Create the puzzle base outline points
    points = []
    
    # Start from top-left
    points.append((x, y))
    
    # Top side with slot
    points.extend([
        (x + 60, y),
        (x + 60, y - 15),  # slot start
        (x + 80, y - 15),  # slot end
        (x + 80, y),
        (x + width, y)
    ])
    
    # Right side with slot
    points.extend([
        (x + width, y + 60),
        (x + width + 15, y + 60),  # slot start
        (x + width + 15, y + 80),  # slot end
        (x + width, y + 80),
        (x + width, y + height)
    ])
    
    # Bottom side with knob
    points.extend([
        (x + 140, y + height),
        (x + 140, y + height + 15),  # knob start
        (x + 120, y + height + 15),  # knob end
        (x + 120, y + height),
        (x, y + height)
    ])
    
    # Left side with knob
    points.extend([
        (x, y + 140),
        (x - 15, y + 140),  # knob start
        (x - 15, y + 120),  # knob end
        (x, y + 120),
        (x, y)
    ])
    
    # Fill with red gradient if fill_height > 0
    if fill_height > 0:
        # Create a simple red fill from bottom up
        fill_points = []
        fill_y = y + height - fill_height
        
        # Simplified fill shape
        fill_points = [
            (x, fill_y),
            (x + width, fill_y),
            (x + width, y + height),
            (x, y + height)
        ]
        
        draw.polygon(fill_points, fill='#ff4444', outline=None)
    
    # Draw the outline
    draw.polygon(points, fill=None, outline='black', width=4)

def create_moving_piece_path(draw, x, y, width, height, fill_height=0):
    """Draw the moving puzzle piece with knobs."""
    points = []
    
    # Start from top-left
    points.append((x, y))
    
    # Top side with knob
    points.extend([
        (x + 30, y),
        (x + 30, y - 15),  # knob start
        (x + 50, y - 15),  # knob end
        (x + 50, y),
        (x + width, y)
    ])
    
    # Right side with knob
    points.extend([
        (x + width, y + 30),
        (x + width + 15, y + 30),  # knob start
        (x + width + 15, y + 50),  # knob end
        (x + width, y + 50),
        (x + width, y + height)
    ])
    
    # Bottom side
    points.extend([
        (x, y + height)
    ])
    
    # Left side back to start
    points.append((x, y))
    
    # Fill with red gradient if fill_height > 0
    if fill_height > 0:
        fill_y = y + height - fill_height
        fill_points = [
            (x, fill_y),
            (x + width, fill_y),
            (x + width, y + height),
            (x, y + height)
        ]
        draw.polygon(fill_points, fill='#ff4444', outline=None)
    
    # Draw the outline
    draw.polygon(points, fill=None, outline='black', width=4)

def ease_in_out_cubic(t):
    """Smooth easing function for animation."""
    return 4 * t * t * t if t < 0.5 else 1 - pow(-2 * t + 2, 3) / 2

def create_frame(frame_num, total_frames):
    """Create a single frame of the animation."""
    # Canvas size
    width, height = 400, 300
    
    # Create image with white background
    img = Image.new('RGB', (width, height), 'white')
    draw = ImageDraw.Draw(img)
    
    # Animation parameters
    join_frames = total_frames // 3
    fill_frames = total_frames - join_frames
    
    # Puzzle positions
    base_x, base_y = 50, 50
    base_width, base_height = 160, 160
    
    piece_start_x, piece_start_y = 280, 20
    piece_end_x, piece_end_y = 170, 90
    piece_width, piece_height = 80, 80
    
    # Calculate current positions and fill
    if frame_num <= join_frames:
        # Moving phase
        progress = frame_num / join_frames
        eased_progress = ease_in_out_cubic(progress)
        
        piece_x = piece_start_x + (piece_end_x - piece_start_x) * eased_progress
        piece_y = piece_start_y + (piece_end_y - piece_start_y) * eased_progress
        base_fill_height = 0
        piece_fill_height = 0
    else:
        # Filling phase
        piece_x = piece_end_x
        piece_y = piece_end_y
        
        fill_progress = (frame_num - join_frames) / fill_frames
        base_fill_height = base_height * fill_progress
        piece_fill_height = piece_height * fill_progress
    
    # Draw puzzle pieces
    create_puzzle_base_path(draw, base_x, base_y, base_width, base_height, base_fill_height)
    create_moving_piece_path(draw, int(piece_x), int(piece_y), piece_width, piece_height, piece_fill_height)
    
    return img

def generate_puzzle_gif():
    """Generate the complete animated GIF."""
    total_frames = 60
    frames = []
    
    print("Generating frames...")
    for i in range(total_frames):
        frame = create_frame(i, total_frames)
        frames.append(frame)
        if i % 10 == 0:
            print(f"Generated frame {i}/{total_frames}")
    
    print("Saving GIF...")
    # Save as animated GIF
    frames[0].save(
        'puzzle_animation.gif',
        save_all=True,
        append_images=frames[1:],
        duration=100,  # 100ms per frame
        loop=0  # Loop forever
    )
    
    print("GIF saved as 'puzzle_animation.gif'")

if __name__ == "__main__":
    try:
        generate_puzzle_gif()
    except ImportError:
        print("PIL (Pillow) is required. Install it with: pip install Pillow")
    except Exception as e:
        print(f"Error generating GIF: {e}")
