<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Puzzle Animation</title>
    <style>
        body {
            margin: 0;
            padding: 20px;
            background: #f0f0f0;
            display: flex;
            justify-content: center;
            align-items: center;
            min-height: 100vh;
            font-family: Arial, sans-serif;
        }
        .container {
            text-align: center;
        }
        canvas {
            border: 2px solid #333;
            background: white;
            margin: 10px;
        }
        .controls {
            margin: 20px 0;
        }
        button {
            padding: 10px 20px;
            font-size: 16px;
            background: #007bff;
            color: white;
            border: none;
            border-radius: 5px;
            cursor: pointer;
            margin: 0 10px;
        }
        button:hover {
            background: #0056b3;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>Animated Puzzle Pieces</h1>
        <canvas id="puzzleCanvas" width="400" height="400"></canvas>
        <div class="controls">
            <button onclick="startAnimation()">Start Animation</button>
            <button onclick="resetAnimation()">Reset</button>
            <button onclick="downloadGif()">Download GIF</button>
        </div>
        <p>Click "Start Animation" to see the puzzle piece join and fill with red!</p>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/gif.js@0.2.0/dist/gif.js"></script>
    <script>
        const canvas = document.getElementById('puzzleCanvas');
        const ctx = canvas.getContext('2d');
        
        let animationFrame = 0;
        let isAnimating = false;
        let animationId;
        let gif;
        let frames = [];
        
        // Animation parameters
        const totalFrames = 120;
        const joinFrames = 40;
        const fillFrames = 80;
        
        // SVG puzzle paths from your provided SVG
        const puzzleBasePath = "M566 2055 c-50 -18 -104 -70 -117 -115 -6 -23 -9 -274 -7 -722 l3 -686 30 -38 c64 -79 16 -74 788 -74 581 0 692 2 716 14 41 22 88 80 100 123 16 56 15 685 -1 701 -8 8 -52 12 -130 12 -134 0 -123 -8 -131 103 -11 161 -194 215 -290 86 -13 -19 -21 -51 -26 -105 l-6 -79 -102 -5 -101 -5 -4 108 c-3 105 -4 110 -31 133 -22 18 -40 24 -78 24 -99 0 -165 52 -156 124 9 66 42 88 136 95 119 9 125 18 129 172 3 83 0 124 -8 134 -18 21 -655 21 -714 0z m674 -128 c0 -59 -5 -99 -13 -109 -8 -12 -33 -17 -84 -20 -53 -3 -80 -9 -100 -24 -71 -53 -92 -133 -52 -204 32 -57 76 -81 157 -87 44 -4 71 -11 80 -21 7 -10 12 -48 12 -103 l0 -87 -111 -4 c-107 -3 -112 -4 -135 -31 -20 -23 -24 -39 -24 -88 0 -71 -19 -113 -60 -134 -40 -21 -72 -19 -105 7 -37 29 -55 76 -55 141 0 45 -4 58 -26 78 -23 22 -36 24 -130 27 l-105 4 3 333 3 334 30 31 c16 17 43 35 60 39 16 5 171 10 343 10 l312 1 0 -93z m478 -463 c35 -24 52 -68 52 -132 0 -49 4 -64 25 -86 23 -25 28 -26 135 -26 l110 0 0 -307 c0 -172 -4 -324 -10 -344 -5 -19 -24 -48 -41 -65 l-30 -29 -335 -5 -335 -5 3 105 3 105 77 5 c86 7 116 22 156 80 15 22 22 48 22 77 0 101 -68 163 -176 163 -33 0 -65 5 -72 12 -8 8 -12 47 -12 110 l0 98 105 0 c99 0 107 2 130 25 21 20 25 34 25 80 0 65 19 116 51 139 30 20 88 21 117 0z m-1037 -254 c15 -9 19 -22 19 -68 0 -67 29 -135 72 -166 16 -11 49 -21 80 -24 44 -3 59 0 87 20 59 40 75 71 79 151 3 51 8 76 20 85 10 7 50 12 109 12 l93 0 0 -110 c0 -107 1 -112 26 -135 22 -21 37 -25 86 -25 95 0 148 -40 148 -111 0 -24 -8 -45 -25 -63 -32 -34 -64 -46 -130 -46 -88 0 -99 -15 -105 -150 l-5 -111 -323 1 c-357 1 -365 2 -402 63 -19 30 -20 52 -20 360 l0 327 86 0 c47 0 94 -5 105 -10z";

        const movingPiecePath = "M1945 2500 c-44 -44 -85 -80 -92 -80 -7 0 -35 20 -63 45 -59 53 -113 71 -167 55 -75 -21 -113 -71 -113 -150 0 -47 5 -64 29 -96 15 -21 38 -48 50 -58 11 -11 21 -26 21 -35 0 -9 -36 -52 -80 -96 -44 -44 -80 -84 -80 -90 0 -5 39 -49 87 -97 79 -80 91 -88 126 -88 30 0 46 8 80 39 81 74 129 81 183 27 55 -55 47 -105 -29 -183 -32 -34 -41 -50 -41 -78 0 -31 11 -46 89 -125 49 -50 93 -90 96 -90 11 0 492 487 505 512 16 28 18 117 3 155 -13 35 -488 513 -509 513 -8 0 -51 -36 -95 -80z m327 -207 c148 -149 229 -238 234 -256 3 -16 4 -47 1 -69 -5 -38 -24 -60 -236 -273 l-231 -231 -70 72 c-39 39 -70 74 -70 78 0 3 21 28 46 57 25 28 50 62 55 76 26 68 -8 161 -71 194 -68 37 -148 19 -209 -45 -19 -20 -44 -36 -56 -36 -12 0 -48 27 -88 68 l-67 68 75 74 c67 66 75 79 75 112 0 31 -9 47 -50 93 -42 46 -50 61 -50 94 0 27 8 49 25 69 46 55 113 48 180 -18 28 -27 54 -43 78 -47 35 -5 40 -2 114 71 43 41 80 76 83 76 3 0 108 -102 232 -227z";

        // Animation parameters
        const scale = 0.15; // Scale down the SVG coordinates
        const baseOffsetX = 50;
        const baseOffsetY = 100;
        const pieceStartX = 250;
        const pieceStartY = 50;
        const pieceEndX = 150;
        const pieceEndY = 80;
        
        function drawPuzzleBase(fillHeight = 0) {
            ctx.save();

            // Transform and scale the SVG coordinates
            ctx.translate(baseOffsetX, baseOffsetY);
            ctx.scale(scale, -scale); // Negative scale to flip Y axis (SVG uses inverted Y)
            ctx.translate(0, -3000); // Adjust for SVG viewBox

            // Create path from SVG data
            const path = new Path2D(puzzleBasePath);

            // Fill with gradient if fillHeight > 0
            if (fillHeight > 0) {
                ctx.save();

                // Reset transform for gradient calculation
                ctx.setTransform(1, 0, 0, 1, 0, 0);

                const actualHeight = 200; // Approximate height after scaling
                const actualY = baseOffsetY + 50; // Approximate Y position

                const gradient = ctx.createLinearGradient(0, actualY + actualHeight, 0, actualY + actualHeight - fillHeight);
                gradient.addColorStop(0, '#ff4444');
                gradient.addColorStop(0.5, '#ff6666');
                gradient.addColorStop(1, '#ff8888');

                // Apply transform again
                ctx.translate(baseOffsetX, baseOffsetY);
                ctx.scale(scale, -scale);
                ctx.translate(0, -3000);

                ctx.fillStyle = gradient;
                ctx.fill(path);
                ctx.restore();
            }

            // Draw outline
            ctx.strokeStyle = '#333';
            ctx.lineWidth = 15; // Thicker line to account for scaling
            ctx.stroke(path);

            ctx.restore();
        }
        
        function drawMovingPiece(x, y, fillHeight = 0) {
            ctx.save();

            // Transform and scale the SVG coordinates for the moving piece
            ctx.translate(x, y);
            ctx.scale(scale, -scale); // Negative scale to flip Y axis
            ctx.translate(0, -3000); // Adjust for SVG viewBox

            // Create path from SVG data
            const path = new Path2D(movingPiecePath);

            // Fill with gradient if fillHeight > 0
            if (fillHeight > 0) {
                ctx.save();

                // Reset transform for gradient calculation
                ctx.setTransform(1, 0, 0, 1, 0, 0);

                const actualHeight = 120; // Approximate height after scaling
                const actualY = y + 30; // Approximate Y position

                const gradient = ctx.createLinearGradient(0, actualY + actualHeight, 0, actualY + actualHeight - fillHeight);
                gradient.addColorStop(0, '#ff4444');
                gradient.addColorStop(0.5, '#ff6666');
                gradient.addColorStop(1, '#ff8888');

                // Apply transform again
                ctx.translate(x, y);
                ctx.scale(scale, -scale);
                ctx.translate(0, -3000);

                ctx.fillStyle = gradient;
                ctx.fill(path);
                ctx.restore();
            }

            // Draw outline
            ctx.strokeStyle = '#333';
            ctx.lineWidth = 15; // Thicker line to account for scaling
            ctx.stroke(path);

            ctx.restore();
        }
        
        function easeInOutCubic(t) {
            return t < 0.5 ? 4 * t * t * t : 1 - Math.pow(-2 * t + 2, 3) / 2;
        }
        
        function drawFrame(frameNum) {
            // Clear canvas
            ctx.clearRect(0, 0, canvas.width, canvas.height);

            let pieceX = pieceStartX;
            let pieceY = pieceStartY;
            let baseFillHeight = 0;
            let pieceFillHeight = 0;

            if (frameNum <= joinFrames) {
                // Moving phase
                const progress = frameNum / joinFrames;
                const easedProgress = easeInOutCubic(progress);

                pieceX = pieceStartX + (pieceEndX - pieceStartX) * easedProgress;
                pieceY = pieceStartY + (pieceEndY - pieceStartY) * easedProgress;
            } else {
                // Filling phase
                pieceX = pieceEndX;
                pieceY = pieceEndY;

                const fillProgress = (frameNum - joinFrames) / fillFrames;
                baseFillHeight = 200 * fillProgress; // Base height
                pieceFillHeight = 120 * fillProgress; // Piece height
            }

            // Draw puzzle pieces
            drawPuzzleBase(baseFillHeight);
            drawMovingPiece(pieceX, pieceY, pieceFillHeight);
        }
        
        function animate() {
            if (!isAnimating) return;
            
            drawFrame(animationFrame);
            
            // Capture frame for GIF
            if (gif && animationFrame % 2 === 0) { // Capture every other frame to reduce file size
                gif.addFrame(canvas, {delay: 100});
            }
            
            animationFrame++;
            
            if (animationFrame >= totalFrames) {
                isAnimating = false;
                if (gif) {
                    gif.render();
                }
                return;
            }
            
            animationId = requestAnimationFrame(animate);
        }
        
        function startAnimation() {
            if (isAnimating) return;
            
            isAnimating = true;
            animationFrame = 0;
            
            // Initialize GIF
            gif = new GIF({
                workers: 2,
                quality: 10,
                width: canvas.width,
                height: canvas.height
            });
            
            gif.on('finished', function(blob) {
                const url = URL.createObjectURL(blob);
                const a = document.createElement('a');
                a.href = url;
                a.download = 'puzzle-animation.gif';
                document.body.appendChild(a);
                a.click();
                document.body.removeChild(a);
                URL.revokeObjectURL(url);
            });
            
            animate();
        }
        
        function resetAnimation() {
            isAnimating = false;
            if (animationId) {
                cancelAnimationFrame(animationId);
            }
            animationFrame = 0;
            drawFrame(0);
        }
        
        function downloadGif() {
            if (!gif) {
                alert('Please run the animation first to generate the GIF!');
                return;
            }
            gif.render();
        }
        
        // Initialize
        drawFrame(0);
    </script>
</body>
</html>
