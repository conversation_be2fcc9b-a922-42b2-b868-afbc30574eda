<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Puzzle Animation</title>
    <style>
        body {
            margin: 0;
            padding: 20px;
            background: #f0f0f0;
            display: flex;
            justify-content: center;
            align-items: center;
            min-height: 100vh;
            font-family: Arial, sans-serif;
        }
        .container {
            text-align: center;
        }
        canvas {
            border: 2px solid #333;
            background: white;
            margin: 10px;
        }
        .controls {
            margin: 20px 0;
        }
        button {
            padding: 10px 20px;
            font-size: 16px;
            background: #007bff;
            color: white;
            border: none;
            border-radius: 5px;
            cursor: pointer;
            margin: 0 10px;
        }
        button:hover {
            background: #0056b3;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>Animated Puzzle Pieces</h1>
        <canvas id="puzzleCanvas" width="400" height="400"></canvas>
        <div class="controls">
            <button onclick="startAnimation()">Start Animation</button>
            <button onclick="resetAnimation()">Reset</button>
            <button onclick="downloadGif()">Download GIF</button>
        </div>
        <p>Click "Start Animation" to see the puzzle piece join and fill with red!</p>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/gif.js@0.2.0/dist/gif.js"></script>
    <script>
        const canvas = document.getElementById('puzzleCanvas');
        const ctx = canvas.getContext('2d');
        
        let animationFrame = 0;
        let isAnimating = false;
        let animationId;
        let gif;
        let frames = [];
        
        // Animation parameters
        const totalFrames = 120;
        const joinFrames = 40;
        const fillFrames = 80;
        
        // Puzzle piece positions
        const puzzleBase = {
            x: 50,
            y: 150,
            width: 200,
            height: 200
        };
        
        const movingPiece = {
            startX: 300,
            startY: 50,
            endX: 200,
            endY: 100,
            width: 100,
            height: 100
        };
        
        function drawPuzzleBase(fillHeight = 0) {
            ctx.save();

            // Create puzzle base path - simplified rectangular base with notches
            ctx.beginPath();

            // Main rectangle outline
            const baseRect = {
                x: puzzleBase.x,
                y: puzzleBase.y,
                w: puzzleBase.width,
                h: puzzleBase.height
            };

            // Draw main shape with notches
            ctx.moveTo(baseRect.x, baseRect.y);

            // Top edge with notch for moving piece
            ctx.lineTo(baseRect.x + 80, baseRect.y);
            ctx.lineTo(baseRect.x + 80, baseRect.y - 20); // notch up
            ctx.lineTo(baseRect.x + 120, baseRect.y - 20);
            ctx.lineTo(baseRect.x + 120, baseRect.y); // notch down
            ctx.lineTo(baseRect.x + baseRect.w, baseRect.y);

            // Right edge with notch
            ctx.lineTo(baseRect.x + baseRect.w, baseRect.y + 80);
            ctx.lineTo(baseRect.x + baseRect.w + 20, baseRect.y + 80); // notch right
            ctx.lineTo(baseRect.x + baseRect.w + 20, baseRect.y + 120);
            ctx.lineTo(baseRect.x + baseRect.w, baseRect.y + 120); // notch left
            ctx.lineTo(baseRect.x + baseRect.w, baseRect.y + baseRect.h);

            // Bottom edge with knob
            ctx.lineTo(baseRect.x + 120, baseRect.y + baseRect.h);
            ctx.lineTo(baseRect.x + 120, baseRect.y + baseRect.h + 20); // knob down
            ctx.lineTo(baseRect.x + 80, baseRect.y + baseRect.h + 20);
            ctx.lineTo(baseRect.x + 80, baseRect.y + baseRect.h); // knob up
            ctx.lineTo(baseRect.x, baseRect.y + baseRect.h);

            // Left edge with knob
            ctx.lineTo(baseRect.x, baseRect.y + 120);
            ctx.lineTo(baseRect.x - 20, baseRect.y + 120); // knob left
            ctx.lineTo(baseRect.x - 20, baseRect.y + 80);
            ctx.lineTo(baseRect.x, baseRect.y + 80); // knob right
            ctx.closePath();

            // Fill with gradient if fillHeight > 0
            if (fillHeight > 0) {
                ctx.save();
                ctx.clip(); // Clip to the puzzle shape

                const gradient = ctx.createLinearGradient(0, baseRect.y + baseRect.h, 0, baseRect.y + baseRect.h - fillHeight);
                gradient.addColorStop(0, '#ff4444');
                gradient.addColorStop(0.5, '#ff6666');
                gradient.addColorStop(1, '#ff8888');

                ctx.fillStyle = gradient;
                ctx.fillRect(baseRect.x - 25, baseRect.y + baseRect.h - fillHeight, baseRect.w + 50, fillHeight + 25);
                ctx.restore();
            }

            // Draw outline
            ctx.strokeStyle = '#333';
            ctx.lineWidth = 4;
            ctx.stroke();

            ctx.restore();
        }
        
        function drawMovingPiece(x, y, fillHeight = 0) {
            ctx.save();

            // Create moving piece path - matches the notches in the base
            ctx.beginPath();

            const pieceRect = {
                x: x,
                y: y,
                w: movingPiece.width,
                h: movingPiece.height
            };

            // Draw the piece that fits into the base's notches
            ctx.moveTo(pieceRect.x, pieceRect.y);

            // Top edge with knob (fits into base's top notch)
            ctx.lineTo(pieceRect.x + 20, pieceRect.y);
            ctx.lineTo(pieceRect.x + 20, pieceRect.y - 20); // knob up
            ctx.lineTo(pieceRect.x + 60, pieceRect.y - 20);
            ctx.lineTo(pieceRect.x + 60, pieceRect.y); // knob down
            ctx.lineTo(pieceRect.x + pieceRect.w, pieceRect.y);

            // Right edge with knob (fits into base's right notch)
            ctx.lineTo(pieceRect.x + pieceRect.w, pieceRect.y + 20);
            ctx.lineTo(pieceRect.x + pieceRect.w + 20, pieceRect.y + 20); // knob right
            ctx.lineTo(pieceRect.x + pieceRect.w + 20, pieceRect.y + 60);
            ctx.lineTo(pieceRect.x + pieceRect.w, pieceRect.y + 60); // knob left
            ctx.lineTo(pieceRect.x + pieceRect.w, pieceRect.y + pieceRect.h);

            // Bottom edge
            ctx.lineTo(pieceRect.x, pieceRect.y + pieceRect.h);

            // Left edge
            ctx.closePath();

            // Fill with gradient if fillHeight > 0
            if (fillHeight > 0) {
                ctx.save();
                ctx.clip(); // Clip to the puzzle piece shape

                const gradient = ctx.createLinearGradient(0, pieceRect.y + pieceRect.h, 0, pieceRect.y + pieceRect.h - fillHeight);
                gradient.addColorStop(0, '#ff4444');
                gradient.addColorStop(0.5, '#ff6666');
                gradient.addColorStop(1, '#ff8888');

                ctx.fillStyle = gradient;
                ctx.fillRect(pieceRect.x - 5, pieceRect.y + pieceRect.h - fillHeight, pieceRect.w + 25, fillHeight + 5);
                ctx.restore();
            }

            // Draw outline
            ctx.strokeStyle = '#333';
            ctx.lineWidth = 4;
            ctx.stroke();

            ctx.restore();
        }
        
        function easeInOutCubic(t) {
            return t < 0.5 ? 4 * t * t * t : 1 - Math.pow(-2 * t + 2, 3) / 2;
        }
        
        function drawFrame(frameNum) {
            // Clear canvas
            ctx.clearRect(0, 0, canvas.width, canvas.height);
            
            let pieceX = movingPiece.startX;
            let pieceY = movingPiece.startY;
            let baseFillHeight = 0;
            let pieceFillHeight = 0;
            
            if (frameNum <= joinFrames) {
                // Moving phase
                const progress = frameNum / joinFrames;
                const easedProgress = easeInOutCubic(progress);
                
                pieceX = movingPiece.startX + (movingPiece.endX - movingPiece.startX) * easedProgress;
                pieceY = movingPiece.startY + (movingPiece.endY - movingPiece.startY) * easedProgress;
            } else {
                // Filling phase
                pieceX = movingPiece.endX;
                pieceY = movingPiece.endY;
                
                const fillProgress = (frameNum - joinFrames) / fillFrames;
                baseFillHeight = puzzleBase.height * fillProgress;
                pieceFillHeight = movingPiece.height * fillProgress;
            }
            
            // Draw puzzle pieces
            drawPuzzleBase(baseFillHeight);
            drawMovingPiece(pieceX, pieceY, pieceFillHeight);
        }
        
        function animate() {
            if (!isAnimating) return;
            
            drawFrame(animationFrame);
            
            // Capture frame for GIF
            if (gif && animationFrame % 2 === 0) { // Capture every other frame to reduce file size
                gif.addFrame(canvas, {delay: 100});
            }
            
            animationFrame++;
            
            if (animationFrame >= totalFrames) {
                isAnimating = false;
                if (gif) {
                    gif.render();
                }
                return;
            }
            
            animationId = requestAnimationFrame(animate);
        }
        
        function startAnimation() {
            if (isAnimating) return;
            
            isAnimating = true;
            animationFrame = 0;
            
            // Initialize GIF
            gif = new GIF({
                workers: 2,
                quality: 10,
                width: canvas.width,
                height: canvas.height
            });
            
            gif.on('finished', function(blob) {
                const url = URL.createObjectURL(blob);
                const a = document.createElement('a');
                a.href = url;
                a.download = 'puzzle-animation.gif';
                document.body.appendChild(a);
                a.click();
                document.body.removeChild(a);
                URL.revokeObjectURL(url);
            });
            
            animate();
        }
        
        function resetAnimation() {
            isAnimating = false;
            if (animationId) {
                cancelAnimationFrame(animationId);
            }
            animationFrame = 0;
            drawFrame(0);
        }
        
        function downloadGif() {
            if (!gif) {
                alert('Please run the animation first to generate the GIF!');
                return;
            }
            gif.render();
        }
        
        // Initialize
        drawFrame(0);
    </script>
</body>
</html>
